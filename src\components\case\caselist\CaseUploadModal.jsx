/* eslint-disable */
import React from 'react';
import PropTypes from 'prop-types';
import {
  Upload,
  Form,
  message,
  Modal,
  Input,
  Icon,
  Row,
  Col,
  TreeSelect,
  Select,
} from 'antd';
const { Dragger } = Upload;
import './index.scss';
import request from '@/utils/axios';
import getQueryString from '@/utils/getCookies';
const getCookies = getQueryString.getCookie;
const { TextArea } = Input;
const { TreeNode } = TreeSelect;

const formItemLayout = {
  labelCol: { span: 6 },
  wrapperCol: { span: 16 },
};

class CaseUploadModal extends React.Component {
  static propTypes = {
    visible: PropTypes.bool,
    onCancel: PropTypes.func,
    onOk: PropTypes.func,
    productId: PropTypes.any,
    record: PropTypes.object, // 当前需求单记录
    caseIds: PropTypes.array, // 当前分类ID
    // cardTree: PropTypes.array, // 分类树数据
    form: PropTypes.object,
    doneApiPrefix: PropTypes.string,
  };

  constructor(props) {
    super(props);
    this.state = {
      caseFile: null, // 保存上传的用例文件
      docList: [], // 需求列表
      docListLoading: false, // 需求列表加载状态
      cardTree: [], // 分类树数据
    };
  }

  componentDidMount() {
    this.getDocList();
    this.getCardTree();
  }

  componentWillReceiveProps(nextProps) {
    if (!nextProps.visible) {
      this.props.form.resetFields();
      this.setState({
        caseFile: null,
      });
    }
  }

  // 获取需求列表
  getDocList = () => {
    this.setState({ docListLoading: true });

    const { productId, caseIds, record } = this.props;

    // 如果record有值且里面有bizId字段，则使用record中的bizId
    let bizId;
    if (record && record.bizId !== undefined && record.bizId !== null) {
      bizId = record.bizId;
    } else {
      // 否则按原来的取值方法取
      bizId = caseIds && caseIds.length > 0
        ? (caseIds.length === 1 && caseIds[0] === 'root' ? -1 : caseIds.join(','))
        : -1;
    }

    request(`${this.props.doneApiPrefix}/case/docList`, {
      method: 'GET',
      params: {
        productLineId: productId,
        channel: 1,
        companyId: productId,
        bizId: bizId
      }
    }).then(res => {
      if (res.code === 200) {
        const docList = (res.data || []).map(doc => ({
          label: doc.docTitle,
          value: doc.docId,
          key: doc.docId,
          ...doc
        }));

        this.setState({
          docList,
          docListLoading: false
        });
      } else {
        message.error('获取需求列表失败: ' + (res.msg || '未知错误'));
        this.setState({ docListLoading: false });
      }
    }).catch(err => {
      message.error('获取需求列表失败');
      this.setState({ docListLoading: false });
    });
  };

  // 获取分类树数据
  getCardTree = () => {
    const { doneApiPrefix, productId } = this.props;

    request(`${doneApiPrefix}/dir/cardTree`, {
      method: 'GET',
      params: {
        productLineId: productId,
        channel: 1,
        companyId: productId,
      },
    }).then(res => {
      this.setState({ cardTree: res.data ? res.data.children : [] });
    });
  };

  handleOk = () => {
    this.props.form.validateFields((err, values) => {
      if (!err) {
        this.submitCaseUpload(values);
      }
    });
  };

  // 提交用例上传
  submitCaseUpload = (values) => {
    const { caseFile } = this.state;

    if (!caseFile) {
      message.error('请上传用例文件');
      return;
    }

    // 检查文件类型
    if (!/(?:xmind)$/i.test(caseFile.name)) {
      message.error('只能上传XMind文件');
      return;
    }

    const formData = new FormData();
    formData.append('file', caseFile);
    formData.append('creator', getCookies('userName'));
    formData.append('title', values.case);
    formData.append('productLineId', Number(this.props.productId));
    // 提交时使用表单中的 requirementId（实际是 docId）
    formData.append('requirementId', undefined);
    formData.append('docId', values.requirementId || '');
    formData.append('description', values.description || '');
    formData.append('channel', 1);
    formData.append('bizId', values.bizId ? values.bizId.join(',') : '-1');
    formData.append('companyId', Number(this.props.productId));
    
    const url = `${this.props.doneApiPrefix}/file/import`;
    request(url, { method: 'POST', body: formData }).then(res => {
      if (res.code === 200) {
        message.success('用例上传成功');
        this.props.onOk && this.props.onOk();
      } else {
        message.error(res.msg || '用例上传失败');
      }
    }).catch(err => {
      console.error('用例上传出错:', err);
      message.error('用例上传失败，请检查网络连接');
    });
  };

  // 渲染分类树节点
  renderTreeNodes = (data = []) =>
    data.map(item => {
      item.title = <span>{item.text}</span>;
      if (item.children) {
        return (
          <TreeNode
            title={item.title}
            value={item.id}
            key={item.id}
            dataRef={item}
          >
            {this.renderTreeNodes(item.children)}
          </TreeNode>
        );
      }
      return <TreeNode {...item} />;
    });

  render() {
    const { visible, onCancel, record, caseIds } = this.props;
    const { caseFile, docList, docListLoading, cardTree } = this.state;
    const { getFieldDecorator } = this.props.form;

    // 文件上传配置
    const uploadProps = {
      accept: '.xmind',
      onRemove: file => {
        this.setState({ caseFile: null });
      },
      beforeUpload: file => {
        // 检查文件类型
        const isXmind = /(?:xmind)$/i.test(file.name);
        if (!isXmind) {
          message.error('只能上传XMind文件(.xmind)');
          return false;
        }

        // 检查文件大小
        const isLt100M = file.size / 1024 / 1024 <= 100;
        if (!isLt100M) {
          message.error('文件大小不能超过100M');
          return false;
        }

        this.setState({ caseFile: file });
        return false;
      },
      fileList: caseFile ? [caseFile] : [],
    };

    // 生成默认用例名称：需求单名称 + 测试用例
    const defaultCaseName = record && record.docTitle
      ? `${record.docTitle}-测试用例`
      : '测试用例';

    // 关联需求的默认值是当前记录的 docId
    const requirementId = record && record.docId ? record.docId : '';
    
    // 用例分类的默认值：优先使用record中的bizId，否则使用caseIds
    let defaultBizId;
    if (record && record.bizId !== undefined && record.bizId !== null) {
      // 如果record中有bizId，将其转换为数组格式
      defaultBizId = Array.isArray(record.bizId) ? record.bizId : [record.bizId];
    } else {
      // 否则按原来的取值方法取
      defaultBizId = caseIds && caseIds.length > 0
        ? (caseIds.length === 1 && caseIds[0] === 'root' ? [-1] : caseIds)
        : [-1];
    }

    return (
      <Modal
        visible={visible}
        onCancel={onCancel}
        onOk={this.handleOk}
        maskClosable={false}
        title="上传用例"
        okText="确认上传"
        cancelText="取消"
        width="600px"
        wrapClassName="case-upload-modal"
      >
        <Form.Item {...formItemLayout} label="用例名称：">
          {getFieldDecorator('case', {
            rules: [{ required: true, message: '请填写用例名称' }],
            initialValue: defaultCaseName,
          })(<Input placeholder="请填写用例名称" maxLength="60" />)}
        </Form.Item>

        <Form.Item {...formItemLayout} label="关联需求：">
          {getFieldDecorator('requirementId', {
            rules: [{ required: true, message: '请选择关联需求' }],
            initialValue: requirementId,
          })(
            <Select
              placeholder="请选择关联需求"
              style={{ width: '100%' }}
              loading={docListLoading}
              showSearch
              optionFilterProp="children"
              filterOption={(input, option) =>
                option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
              }
            >
              {docList.map(doc => (
                <Select.Option
                  key={doc.key}
                  value={doc.value}
                  disabled={doc.docStatus === 1 || doc.docStatus === 2}
                >
                  {doc.label}
                  {(doc.docStatus === 1 || doc.docStatus === 2) && (
                    <span style={{ color: '#999', marginLeft: '8px' }}>
                      ({doc.docStatus === 1 ? 'review中' : '已review'})
                    </span>
                  )}
                </Select.Option>
              ))}
            </Select>
          )}
        </Form.Item>

        <Form.Item {...formItemLayout} label="用例分类：">
          {getFieldDecorator('bizId', {
            rules: [{ required: true, message: '请选择用例分类' }],
            initialValue: defaultBizId,
          })(
            <TreeSelect
              style={{ width: '100%' }}
              dropdownStyle={{ maxHeight: 400, overflow: 'auto' }}
              placeholder="请选择用例分类"
              allowClear
              multiple
              treeDefaultExpandAll
              disabled // 禁用，只显示当前分类
            >
              {this.renderTreeNodes(cardTree)}
            </TreeSelect>,
          )}
        </Form.Item>

        {/* <Form.Item {...formItemLayout} label="描述：">
          {getFieldDecorator('description', {
            initialValue: '',
          })(<TextArea autoSize={{ minRows: 4, maxRows: 8 }} maxLength="500" />)}
        </Form.Item> */}

        <Row style={{ marginBottom: '20px' }}>
          <Col span={6}>导入用例文件:</Col>
          <Col span={16} className="dragger">
            <div className="div-flex-child-1">
              <Dragger {...uploadProps}>
                {caseFile === null ? (
                  <Icon
                    type="plus-circle"
                    style={{ color: '#447CE6', fontSize: '24px' }}
                  />
                ) : (
                  <Icon
                    type="file"
                    style={{
                      color: '#447CE6',
                      fontSize: '24px',
                      position: 'relative',
                      top: '-15px',
                    }}
                  />
                )}
              </Dragger>
            </div>
            <div className="div-flex-child-2">
              <div>
                <span className="span-text span-text-bold">
                  上传XMind文件（必传）
                </span>
                <span className="span-text span-text-light">
                  只支持.xmind文件，文件大小不超过100M
                </span>
              </div>
            </div>
          </Col>
        </Row>
      </Modal>
    );
  }
}

export default Form.create()(CaseUploadModal);
