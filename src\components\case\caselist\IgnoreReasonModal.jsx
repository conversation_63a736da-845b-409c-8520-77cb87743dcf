import React from 'react';
import { Modal, Form, Input, message } from 'antd';
import PropTypes from 'prop-types';

const { TextArea } = Input;

class IgnoreReasonModal extends React.Component {
  static propTypes = {
    visible: PropTypes.bool.isRequired,
    onCancel: PropTypes.func.isRequired,
    onOk: PropTypes.func.isRequired,
    record: PropTypes.object,
    form: PropTypes.object.isRequired,
  };

  handleOk = () => {
    this.props.form.validateFields((err, values) => {
      if (!err) {
        if (!values.ignoreReason || values.ignoreReason.trim() === '') {
          message.error('请填写忽略原因');
          return;
        }
        this.props.onOk({
          ...values,
          caseId: this.props.record?.id,
        });
      }
    });
  };

  handleCancel = () => {
    this.props.form.resetFields();
    this.props.onCancel();
  };

  render() {
    const { visible, record, form } = this.props;
    const { getFieldDecorator } = form;

    return (
      <Modal
        title="忽略用例"
        visible={visible}
        onOk={this.handleOk}
        onCancel={this.handleCancel}
        okText="确认忽略"
        cancelText="取消"
        width={500}
        destroyOnClose
      >
        <div style={{ marginBottom: 16 }}>
          <strong>用例名称：</strong>
          <span>{record?.caseTitle || '-'}</span>
        </div>
        <div style={{ marginBottom: 16 }}>
          <strong>需求单：</strong>
          <span>{record?.docTitle || '-'}</span>
        </div>
        <Form layout="vertical">
          <Form.Item label="忽略原因" required>
            {getFieldDecorator('ignoreReason', {
              rules: [
                { required: true, message: '请填写忽略原因' },
                { max: 500, message: '忽略原因不能超过500个字符' },
              ],
            })(
              <TextArea
                rows={4}
                placeholder="请详细说明忽略此用例的原因..."
                maxLength={500}
                showCount
              />
            )}
          </Form.Item>
        </Form>
      </Modal>
    );
  }
}

export default Form.create()(IgnoreReasonModal);
