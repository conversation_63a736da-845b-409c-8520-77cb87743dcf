/* eslint-disable */
import React from 'react';
import PropTypes from 'prop-types';
import request from '@/utils/axios';
import { Row, Button, Col, Icon, Form, message, Radio } from 'antd';
import './index.scss';
import _ from 'lodash';
import CaseModal from './caseModal.js';
import List from './list.js';
import Filter from './filter.js';
import OeFilter from './oefilter';
import FileTree from './tree';

import RestoreModal from './restoreModal';
import RequirementUploadModal from './RequirementUploadModal';
class CaseLists extends React.Component {
  static propTypes = {
    form: PropTypes.any,
    productId: PropTypes.any,
    updateCallBack: PropTypes.any,
    users: PropTypes.any,
  };
  constructor(props) {
    super(props);
    this.fileTreeRef = React.createRef(); // 添加 ref
    this.state = {
      list: [],
      total: 0, // 数据条数
      record: {},
      title: '',
      visible: false,
      iterationList: [], // 需求列表
      showFilterBox: false, // 展示筛选框
      productMember: [], // 所有人
      currCase: null, // 当前选中case
      showAddRecord: false, // 展开添加记录弹框
      envList: [], // 执行记录环境列表
      options: { projectLs: [], requirementLs: [] },
      requirement: null,
      filterStatus: 'filter-hide',
      filterVisble: false,
      loading: true,
      current: 1,
      productLineId: '',
      treeData: [],
      levelId: '',
      levelText: '',
      searchValue: '',
      autoExpandParent: true,
      dataList: [],
      caseIds: ['root'],
      isSelect: true,
      isSibling: true,
      isAdd: true,
      isReName: true,
      treeSelect: [],
      treeData: [],
      companyName: '', // 添加公司名称状态
      isDeleteFolder: false, // 是否是删除文件夹
      selectedRows: [], // 添加选中行状态
      restoreModalVisible: false,
      restoreFormData: {
        selectedCases: [],
        caseIds: [],
      },
      selectedCasesInfo: new Map(), // 使用 Map 存储选中项的详细信息
      pageSize: 10, // 添加默认每页显示条数
      current: 1, // 当前第几页
      viewMode: 'default', // 视图模式：'default' 默认视图, 'requirement' 需求视图
      enableStatusView: true, // 是否启用状态视图功能
      requirementUploadVisible: false, // 需求上传弹窗显示状态
      cardTree: [], // 分类树数据
    };
  }
  componentDidMount() {
    const savedNodeId = sessionStorage.getItem('selectedTreeNode');
    this.setState(
      {
        productLineId: this.props.match.params.productLineId,
      },
      () => {
        this.getProductMumber();
        this.getCompanyInfo(); // 添加这行
        // this.getTreeList();
        // 获取树数据时传入保存的节点ID
        this.getTreeList(false, savedNodeId);
        // 移除页面加载时的 getCardTree 调用，改为在需求弹窗打开时调用
      },
    );
  }
  componentWillReceiveProps(nextProps) {
    if (
      this.props.match.params.productLineId !=
      nextProps.match.params.productLineId
    ) {
      this.setState(
        {
          productLineId: nextProps.match.params.productLineId,
        },
        () => {
          this.getCaseList(1, '', '', '', []);
          this.getProductMumber();
          this.getCompanyInfo(); // 添加这行
        },
      );
    }
  }
  getTreeList = (isManual, savedNodeId) => {
    const { productLineId, caseIds } = this.state;
    const { doneApiPrefix } = this.props;
    return request(`${doneApiPrefix}/dir/list`, {
      method: 'GET',
      params: {
        productLineId,
        channel: 1,
        companyId: productLineId,
      },
    }).then(res => {
      if (res.code === 200) {
        // 对数据进行排序处理
        const sortedChildren = res.data.children.sort((a, b) => {
          // 如果是删除文件夹，放到最后
          if (a.id === 'delete') return 1;
          if (b.id === 'delete') return -1;
          // 其他文件夹按原有顺序
          return 0;
        });
         // 使用保存的节点ID或默认的root
         const nodeId = savedNodeId || 'root';
        this.setState(
          {
            treeData: sortedChildren,
            // caseIds:
            //   this.state.treeSelect.length > 0
            //     ? this.state.treeSelect.toString()
            //     : caseIds,
            caseIds: nodeId,
          },
          () => {
            if (!isManual) this.getCaseList(1, '', '', '', []);
          },
        );
      } else {
        message.error(res.msg);
        // this.setState({
        //   treeData: [],
        //   caseIds:
        //   this.state.treeSelect.length > 0
        //     ? this.state.treeSelect.toString()
        //     : caseIds,
        // },
        // () => {
        //   if (!isManual) this.getCaseList(1, '', '', '', []);
        // },);
      }
      return null;
    });
  };
  handlePageChange = (page, pageSize) => {
    this.setState({
      current: page,
      pageSize: pageSize
    });
  };

  getCaseList = (
    current,
    nameFilter,
    createrFilter,
    iterationFilter,
    choiseDate = [],
    caseKeyWords = '',
    pageSize,
  ) => {
    const size = pageSize || this.state.pageSize;
    const { caseIds, viewMode } = this.state;

    // 统一使用同一个接口，通过 viewType 参数区分视图类型
    // viewType: 0 = 用例视图, 1 = 需求视图
    const viewType = viewMode === 'requirement' ? 1 : 0;

    request(`${this.props.doneApiPrefix}/case/list`, {
      method: 'GET',
      params: {
        pageSize: size, // 使用当前页码大小
        pageNum: current,
        productLineId: this.state.productLineId,
        caseType: 0,
        title: nameFilter || '',
        creator: createrFilter || '',
        channel: 1,
        requirementId: iterationFilter || '',
        beginTime: choiseDate.length > 0 ? `${choiseDate[0]} 00:00:00` : '',
        endTime: choiseDate.length > 0 ? `${choiseDate[1]}  23:59:59` : '',
        bizId: caseIds ? caseIds : 'root',
        caseKeyWords: caseKeyWords || '',
        companyId: this.state.productLineId,
        viewType: viewType, // 添加视图类型参数
      },
    }).then(res => {
      if (res.code === 200) {
        this.setState({
          list: res.data.dataSources,
          total: res.data.total,
          current,
          pageSize:size,
          nameFilter,
          createrFilter,
          iterationFilter,
          choiseDate,
          caseKeyWords,
        });
      } else {
        message.error(res.msg);
        this.setState({
          list: [],
          total: 0,
          current: 1,
          pageSize:size,
          nameFilter: '',
          createrFilter: '',
          iterationFilter: '',
          choiseDate: [],
          caseKeyWords: '',
        });
      }
      this.setState({ loading: false });
      return null;
    });
  };

  initCaseModalInfo = () => {
    let { requirementLs } = this.state;
    let requirement = null;
    this.setState({
      options: {
        requirement,
        requirementLs,
      },
    });
  };
  getProductMumber = () => {
    let url = `${this.props.doneApiPrefix}/case/listCreators`;
    request(url, {
      method: 'GET',
      params: {
        productLineId: this.state.productLineId,
        caseType: 0,
        companyId: this.state.productLineId,
      },
    }).then(res => {
      if (res.code === 200) {
        this.setState({
          productMember: res.data,
        });
      }
    });
  };
  getCompanyInfo = () => {
    const { productLineId } = this.state;
    const { doneApiPrefix } = this.props;
    request(`${doneApiPrefix}/company/get`, {
      method: 'GET',
      params: {
        companyId: productLineId,
      },
    }).then(res => {
      if (res.code === 200) {
        this.setState({
          companyName: res.data.name,
          enableStatusView: res.data.docEnable=== 1, // 根据接口返回值决定是否启用
        });
      } else {
        // message.error(res.msg);
        this.setState({
          companyName: '',
          enableStatusView: false, // 默认不启用状态视图
        });
      }
    });
  };
  handleTask = (val, record, project, requirement, current) => {
    this.setState(
      {
        visible: true,
        title: val,
        currCase: record,
        project,
        requirement,
        current,
      },
      () => {
        this.props.form.resetFields();
      },
    );
  };
  onShowFilterBoxClick = () => {
    let showFilterBox = !this.state.showFilterBox;
    this.setState({
      showFilterBox,
      iterationFilter: '',
      nameFilter: '',
      choiseDate: [],
      createrFilter: '',
      caseKeyWords: '',
    });
  };
  onClose = vis => {
    this.setState({ visible: vis });
  };

  filterHandler = () => {
    this.setState({ filterStatus: 'filter-show', filterVisble: true });
  };

  closeFilter = () => {
    this.setState({ filterStatus: 'filter-hide', filterVisble: false });
  };
  handleSelectedRowsChange = selectedRowKeys => {
    // 更新选中项信息
    const selectedCasesInfo = new Map(this.state.selectedCasesInfo);

    // 添加新选中项的信息
    this.state.list.forEach(item => {
      if (selectedRowKeys.includes(item.id)) {
        selectedCasesInfo.set(item.id, item);
      }
    });

    // 移除取消选中的项
    Array.from(selectedCasesInfo.keys()).forEach(key => {
      if (!selectedRowKeys.includes(key)) {
        selectedCasesInfo.delete(key);
      }
    });

    this.setState({
      selectedRows: selectedRowKeys,
      selectedCasesInfo,
    });
  };

  showRestoreModal = singleCaseId => {
    let selectedIds;
    let selectedCases;

    if (singleCaseId) {
      // 单个恢复
      selectedIds = [singleCaseId];
      selectedCases = this.state.list.filter(item => item.id === singleCaseId);
    } else {
      // 批量恢复
      selectedIds = this.state.selectedRows;
      selectedCases = Array.from(this.state.selectedCasesInfo.values());
    }

    this.setState({
      restoreModalVisible: true,
      restoreFormData: {
        selectedCases,
        caseIds: selectedIds,
      },
    });
  };

  handleRestoreSuccess = () => {
    this.setState(
      {
        selectedRows: [],
        selectedCasesInfo: new Map(),
        restoreModalVisible: false,
        caseIds: ['root'],
        isDeleteFolder: false, // 退出删除文件夹状态
      },
      () => {
        this.getTreeList(true);
        this.getCaseList(1, '', '', '', []);
        // 然后通过 ref 调用 FileTree 组件的方法选中 root 节点
        if (this.fileTreeRef.current) {
          this.fileTreeRef.current.selectRootNode();
        }
      },
    );
  };

  // 视图模式切换方法
  handleViewModeChange = (e) => {
    this.setState({
      viewMode: e.target.value,
    }, () => {
      // 切换视图模式后重新加载数据
      this.getCaseList(1, '', '', '', []);
    });
  };

  // 获取分类树数据
  getCardTree = () => {
    const { doneApiPrefix } = this.props;
    const { productLineId } = this.state;

    request(`${doneApiPrefix}/dir/cardTree`, {
      method: 'GET',
      params: {
        productLineId: productLineId,
        channel: 1,
        companyId: productLineId,
      },
    }).then(res => {
      this.setState({ cardTree: res.data ? res.data.children : [] });
    });
  };

  // 显示需求上传弹窗
  showRequirementUploadModal = () => {
    // 在弹窗打开时获取分类树数据
    this.getCardTree();
    this.setState({
      requirementUploadVisible: true,
    });
  };

  // 关闭需求上传弹窗
  hideRequirementUploadModal = () => {
    this.setState({
      requirementUploadVisible: false,
    });
  };

  // 需求上传成功回调
  handleRequirementUploadSuccess = () => {
    this.setState({
      requirementUploadVisible: false,
    });
    // 刷新列表数据
    this.getCaseList();
    message.success('需求上传成功');
  };

  // 需求视图中的新建用例（打开用例上传弹窗）
  handleCreateCaseInRequirementView = () => {
    // 触发 List 组件中的新建用例功能
    if (this.listRef && this.listRef.handleCreateCaseFromHeader) {
      this.listRef.handleCreateCaseFromHeader();
    } else {
      message.info('请在需求列表中选择具体需求后创建用例');
    }
  };
  render() {
    const {
      requirement,
      list,
      total,
      productMember,
      filterVisble,
      filterStatus,
      nameFilter,
      createrFilter,
      iterationFilter,
      choiseDate,
      treeData,
      caseIds,
      caseKeyWords,
      companyName,
      selectedRows,
      loading,
      //新增
      restoreModalVisible,
      restoreFormData,
      isDeleteFolder,
    } = this.state;
    const { match, doneApiPrefix } = this.props;
    const { productLineId } = match.params;
    return (
      <div className="all-content">
        <FileTree
          ref={this.fileTreeRef}
          productLineId={Number(productLineId)}
          doneApiPrefix={doneApiPrefix}
          getCaseList={caseIds => {
            const isDeleteFolder = caseIds.toString() === 'delete';
            // console.log(caseIds, 'caseIds');
            // console.log(isDeleteFolder, 'isDeleteFolder');
            this.setState({ caseIds, isDeleteFolder }, () => {
              this.getCaseList(1, '', '', '');
            });
          }}
          getTreeList={this.getTreeList}
          treeData={treeData}
          // defaultSelectedKeys={['root']}
        />
        <div className="min-hig-content">
          <div className="site-drawer-render-in-current-wrapper">
            <Row className="m-b-10">
              <Col span={18}>
                <div style={{ margin: '10px', display: 'flex', alignItems: 'center' }}>
                  <span style={{ marginRight: '20px' }}>
                    项目名称：{companyName}
                  </span>
                  <span style={{ marginRight: '20px' }}>
                    快速筛选：<a>全部({total})</a>
                  </span>
                  {this.state.enableStatusView && (
                    <div className="view-mode-switch">
                      <span style={{ marginRight: '8px' }}>视图模式：</span>
                      <Radio.Group
                        value={this.state.viewMode}
                        onChange={this.handleViewModeChange}
                        size="small"
                      >
                        <Radio.Button value="default">默认视图</Radio.Button>
                        <Radio.Button value="requirement">需求视图</Radio.Button>
                      </Radio.Group>
                    </div>
                  )}
                </div>
              </Col>
              <Col xs={6} className="text-right">
                <Button
                  style={{ marginRight: 16 }}
                  onClick={this.filterHandler}
                >
                  <Icon type="filter" /> 筛选
                </Button>
                {/* <Button
                  type="primary"
                  onClick={() => {
                    this.handleTask('add');
                    this.setState({
                      currCase: null,
                      visible: true,
                      project: null,
                      requirement: null,
                    });
                  }}
                >
                  <Icon type="plus" /> 新建用例
                </Button> */}
                {(this.state.isDeleteFolder || sessionStorage.getItem('selectedTreeNode')=='delete') ? (
                  <Button
                    type="primary"
                    disabled={selectedRows.length === 0}
                    onClick={() => this.showRestoreModal()}
                  >
                    <Icon type="undo" /> 恢复用例
                  </Button>
                ) : (
                  <>
                    {this.state.viewMode === 'requirement' ? (
                      // 需求视图：显示两个按钮
                      <>
                        <Button
                          type="primary"
                          onClick={this.handleCreateCaseInRequirementView}
                          style={{ marginRight: '8px' }}
                        >
                          <Icon type="plus" />
                          新建用例
                        </Button>
                        <Button
                          type="primary"
                          onClick={this.showRequirementUploadModal}
                          style={{ backgroundColor: '#52c41a', borderColor: '#52c41a' }}
                        >
                          <Icon type="upload" />
                          需求上传
                        </Button>
                      </>
                    ) : (
                      // 默认视图：显示新建用例按钮
                      <Button
                        type="primary"
                        onClick={() => {
                          this.handleTask('add');
                          this.setState({
                            currCase: null,
                            visible: true,
                            project: null,
                            requirement: null,
                          });
                        }}
                      >
                        <Icon type="plus" />
                        新建用例
                      </Button>
                    )}
                  </>
                )}
              </Col>
            </Row>
            <hr
              style={{ border: '0', backgroundColor: '#e8e8e8', height: '1px' }}
            />
            {this.state.showFilterBox && (
              <Filter
                getCaseList={this.getCaseList}
                productMember={productMember}
              />
            )}
            <List
              ref={ref => this.listRef = ref}
              productId={productLineId}
              options={this.state.options}
              list={list}
              total={total}
              handleTask={this.handleTask}
              getCaseList={this.getCaseList}
              getTreeList={this.getTreeList}
              type={this.props.type}
              loading={this.state.loading}
              baseUrl={this.props.baseUrl}
              oeApiPrefix={this.props.oeApiPrefix}
              doneApiPrefix={this.props.doneApiPrefix}
              current={this.state.current}
              nameFilter={nameFilter}
              caseKeyWords={caseKeyWords}
              createrFilter={createrFilter}
              iterationFilter={iterationFilter}
              choiseDate={choiseDate}
              isDeleteFolder={this.state.isDeleteFolder} // 是否是删除文件夹
              selectedRows={selectedRows}
              onSelectedRowsChange={this.handleSelectedRowsChange}
              onRestore={caseId => this.showRestoreModal(caseId)}
              pageSize={this.state.pageSize}
              // current={this.state.current}
              onPageChange={this.handlePageChange}
              viewMode={this.state.viewMode} // 传递视图模式
              caseIds={[caseIds]} // 传递当前分类ID
            ></List>

            {(filterVisble && (
              <OeFilter
                onCancel={this.closeFilter}
                getCaseList={this.getCaseList}
                productMember={productMember}
                filterStatus={filterStatus}
                closeFilter={this.closeFilter}
                visible={filterVisble}
                oeApiPrefix={this.props.oeApiPrefix}
                productId={productLineId}
              />
            )) ||
              null}
          </div>

          {this.state.visible && (
            <CaseModal
              productId={productLineId}
              data={this.state.currCase}
              title={this.state.title}
              requirement={requirement}
              options={this.state.options}
              show={this.state.visible}
              onClose={this.onClose}
              oeApiPrefix={this.props.oeApiPrefix}
              doneApiPrefix={this.props.doneApiPrefix}
              baseUrl={this.props.baseUrl}
              onUpdate={() => {
                // this.getCaseList(this.state.current || 1, '', '', '', []);
                this.getTreeList();
                this.setState({ currCase: null, visible: false });
              }}
              type={this.props.type}
              caseIds={[caseIds]}
            />
          )}
        </div>
        <RestoreModal
          visible={restoreModalVisible}
          onCancel={() => this.setState({ restoreModalVisible: false })}
          onSuccess={this.handleRestoreSuccess}
          formData={restoreFormData}
          doneApiPrefix={this.props.doneApiPrefix}
          productLineId={productLineId}
        />
        <RequirementUploadModal
          visible={this.state.requirementUploadVisible}
          onCancel={this.hideRequirementUploadModal}
          onOk={this.handleRequirementUploadSuccess}
          productId={productLineId}
          selectedCaseIds={[caseIds]}
          cardTree={this.state.cardTree}
          apiPrefix={this.props.doneApiPrefix}
        />
      </div>
    );
  }
}
export default Form.create()(CaseLists);
