/* eslint-disable */
import React from 'react';
import PropTypes from 'prop-types';
import { Modal, Table } from 'antd';

class RequirementErrorModal extends React.Component {
  static propTypes = {
    visible: PropTypes.bool,
    onCancel: PropTypes.func,
    errorData: PropTypes.array, // 错误数据
  };

  render() {
    const { visible, onCancel, errorData = [] } = this.props;

    // 表格列配置
    const columns = [
      {
        title: '需求单',
        dataIndex: 'docTitle',
        key: 'docTitle',
        width: '60%',
        render: text => (
          <div style={{ wordBreak: 'break-all', whiteSpace: 'normal' }}>
            {text || '-'}
          </div>
        ),
      },
      {
        title: '错误信息',
        dataIndex: 'error',
        key: 'error',
        width: '40%',
        render: text => (
          <div style={{ 
            wordBreak: 'break-all', 
            whiteSpace: 'normal',
            color: '#f5222d'
          }}>
            {text || '-'}
          </div>
        ),
      },
    ];

    return (
      <Modal
        visible={visible}
        onCancel={onCancel}
        footer={null}
        title="需求文件校验失败"
        width="800px"
        wrapClassName="requirement-error-modal"
      >
        <div style={{ marginBottom: '16px', color: '#f5222d' }}>
          以下需求信息校验未通过，请修改后重新上传：
        </div>
        <Table
          columns={columns}
          dataSource={errorData}
          rowKey={(record, index) => record.docId || index}
          pagination={false}
          size="middle"
          scroll={{ y: 400 }}
          bordered
        />
      </Modal>
    );
  }
}

export default RequirementErrorModal;
